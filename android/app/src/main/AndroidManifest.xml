<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">


    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />


    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <uses-permission android:name="android.permission.BUBBLE_PERMISSION" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <uses-permission-sdk-23 android:name="android.permission.VIBRATE" />

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission
        android:name="android.permission.READ_MEDIA_IMAGES"
        tools:ignore="SelectedPhotoAccess" />
    <uses-permission
        android:name="android.permission.READ_MEDIA_VIDEO"
        tools:ignore="SelectedPhotoAccess" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"
        tools:ignore="ScopedStorage" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"
        tools:ignore="SelectedPhotoAccess" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- Request legacy
    Bluetooth permissions on older devices. -->
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <!-- MIUI-specific permissions for better performance -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="miui.permission.USE_INTERNAL_GENERAL_API" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS"
        tools:ignore="ProtectedPermissions" />

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:mimeType="*/*" />
        </intent>
    </queries>

    <application
        android:name=".MainApplication"
        android:allowBackup="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="false"
        android:icon="@mipmap/app_launch_icon_round"
        android:installLocation="auto"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:roundIcon="@mipmap/app_launch_icon_round"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:hardwareAccelerated="true"
        android:largeHeap="true"
        tools:replace="android:allowBackup">

        <activity
            android:name="com.mattermost.rnbeta.Call_Activity"
            android:exported="false" />
        <activity
            android:name="com.mattermost.rnbeta.Call_View"
            android:exported="false"
            android:theme="@style/AppTheme" />

        <meta-data
            android:name="firebase_analytics_collection_deactivated"
            android:value="true" />
        <meta-data
            android:name="android.content.APP_RESTRICTIONS"
            android:resource="@xml/app_restrictions" />
        <meta-data
            android:name="miui.performance.config"
            android:resource="@xml/miui_performance_config" />
        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />
        <meta-data
            android:name="android.allow_multiple_resumed_activities"
            android:value="true" />

        <activity
            android:name=".MainActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity=""
            android:windowSoftInputMode="adjustResize"
            android:hardwareAccelerated="true"
            android:process=":main"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />

                <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="mattermost" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="mmauthbeta" />
            </intent-filter>
        </activity>

        <service
            android:name=".NotificationDismissService"
            android:enabled="true"
            android:exported="false" />

        <receiver
            android:name=".NotificationReplyBroadcastReceiver"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name="com.reactnativenavigation.controllers.NavigationActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:exported="false"
            android:resizeableActivity="true" />
        <activity
            android:name="com.mattermost.rnshare.ShareActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:exported="true"
            android:taskAffinity="com.mattermost.share"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <action android:name="android.intent.action.SEND_MULTIPLE" />

                <category android:name="android.intent.category.DEFAULT" />
                <!-- for sharing -->
                <data android:mimeType="*/*" />
            </intent-filter>
        </activity> <!--
        For Calls microphone to work in the background -->
        <service
            android:name="com.voximplant.foregroundservice.VIForegroundService"
            android:exported="false"
            android:foregroundServiceType="microphone" /> <!-- Android 14 requires the correct
        Foreground Service (FGS) type for RNShare -->
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:exported="false"
            android:foregroundServiceType="dataSync"
            android:stopWithTask="false"
            tools:node="merge" />
        <service android:name="com.mattermost.rnbeta.SoundService"
        />

        <activity android:name="com.ahmedadeltito.photoeditor.PhotoEditorActivity" />
        <activity
            android:name="com.ahmedadeltito.photoeditor.secondActivity.PhotoEditorActivitySecond" />
        <activity android:name="com.yalantis.ucrop.UCropActivity" />

        <receiver
            android:name=".helpers.NotificationReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="ACCEPT_CALL" />
                <action android:name="DECLINE_CALL" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
