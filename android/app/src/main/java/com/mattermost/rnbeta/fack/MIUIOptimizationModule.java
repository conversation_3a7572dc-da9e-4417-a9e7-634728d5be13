package com.mattermost.rnbeta.fack;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.PowerManager;
import android.provider.Settings;
import android.util.Log;

import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.BaseActivityEventListener;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableNativeMap;

import java.lang.reflect.Method;

public class MIUIOptimizationModule extends ReactContextBaseJavaModule {
    private static final String TAG = "MIUIOptimization";
    private static final int REQUEST_BATTERY_OPTIMIZATION = 1001;
    private Promise batteryOptimizationPromise;

    private final ActivityEventListener activityEventListener = new BaseActivityEventListener() {
        @Override
        public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent intent) {
            if (requestCode == REQUEST_BATTERY_OPTIMIZATION && batteryOptimizationPromise != null) {
                batteryOptimizationPromise.resolve(resultCode == Activity.RESULT_OK);
                batteryOptimizationPromise = null;
            }
        }
    };

    public MIUIOptimizationModule(ReactApplicationContext reactContext) {
        super(reactContext);
        reactContext.addActivityEventListener(activityEventListener);
    }

    @Override
    public String getName() {
        return "MIUIOptimization";
    }

    /**
     * Check if the device is running MIUI
     */
    @ReactMethod
    public void isMIUIDevice(Promise promise) {
        try {
            boolean isMIUI = isMIUISystem();
            WritableMap result = new WritableNativeMap();
            result.putBoolean("isMIUI", isMIUI);
            result.putString("brand", Build.BRAND);
            result.putString("manufacturer", Build.MANUFACTURER);
            result.putString("model", Build.MODEL);
            promise.resolve(result);
        } catch (Exception e) {
            promise.reject("ERROR", "Failed to check MIUI device", e);
        }
    }

    /**
     * Request battery optimization exclusion
     */
    @ReactMethod
    public void requestBatteryOptimizationExclusion(Promise promise) {
        try {
            Activity currentActivity = getCurrentActivity();
            if (currentActivity == null) {
                promise.reject("ERROR", "No current activity");
                return;
            }

            PowerManager pm = (PowerManager) getReactApplicationContext().getSystemService(Context.POWER_SERVICE);
            String packageName = getReactApplicationContext().getPackageName();

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!pm.isIgnoringBatteryOptimizations(packageName)) {
                    Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                    intent.setData(Uri.parse("package:" + packageName));
                    
                    batteryOptimizationPromise = promise;
                    currentActivity.startActivityForResult(intent, REQUEST_BATTERY_OPTIMIZATION);
                } else {
                    promise.resolve(true);
                }
            } else {
                promise.resolve(true);
            }
        } catch (Exception e) {
            promise.reject("ERROR", "Failed to request battery optimization exclusion", e);
        }
    }

    /**
     * Check if app is excluded from battery optimizations
     */
    @ReactMethod
    public void isBatteryOptimizationIgnored(Promise promise) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PowerManager pm = (PowerManager) getReactApplicationContext().getSystemService(Context.POWER_SERVICE);
                String packageName = getReactApplicationContext().getPackageName();
                boolean isIgnored = pm.isIgnoringBatteryOptimizations(packageName);
                promise.resolve(isIgnored);
            } else {
                promise.resolve(true);
            }
        } catch (Exception e) {
            promise.reject("ERROR", "Failed to check battery optimization status", e);
        }
    }

    /**
     * Open MIUI autostart settings
     */
    @ReactMethod
    public void openAutoStartSettings(Promise promise) {
        try {
            Activity currentActivity = getCurrentActivity();
            if (currentActivity == null) {
                promise.reject("ERROR", "No current activity");
                return;
            }

            Intent intent = new Intent();
            intent.setClassName("com.miui.securitycenter", 
                "com.miui.permcenter.autostart.AutoStartManagementActivity");
            
            if (isIntentAvailable(intent)) {
                currentActivity.startActivity(intent);
                promise.resolve(true);
            } else {
                // Fallback to general settings
                intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(Uri.parse("package:" + getReactApplicationContext().getPackageName()));
                currentActivity.startActivity(intent);
                promise.resolve(false);
            }
        } catch (Exception e) {
            promise.reject("ERROR", "Failed to open autostart settings", e);
        }
    }

    /**
     * Enable high performance mode
     */
    @ReactMethod
    public void enableHighPerformanceMode(Promise promise) {
        try {
            // This is a placeholder for MIUI-specific performance optimizations
            // In a real implementation, you would use MIUI-specific APIs
            Log.d(TAG, "Enabling high performance mode for MIUI");
            
            // You can add MIUI-specific performance tweaks here
            optimizeForMIUI();
            
            promise.resolve(true);
        } catch (Exception e) {
            promise.reject("ERROR", "Failed to enable high performance mode", e);
        }
    }

    /**
     * Get MIUI version information
     */
    @ReactMethod
    public void getMIUIVersion(Promise promise) {
        try {
            WritableMap result = new WritableNativeMap();
            
            if (isMIUISystem()) {
                String miuiVersion = getMIUIVersionName();
                result.putString("version", miuiVersion);
                result.putBoolean("isMIUI", true);
            } else {
                result.putBoolean("isMIUI", false);
            }
            
            promise.resolve(result);
        } catch (Exception e) {
            promise.reject("ERROR", "Failed to get MIUI version", e);
        }
    }

    // Helper methods

    private boolean isMIUISystem() {
        try {
            Class<?> clazz = Class.forName("miui.os.Build");
            return clazz != null;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    private String getMIUIVersionName() {
        try {
            Class<?> clazz = Class.forName("miui.os.Build");
            Method method = clazz.getMethod("getVersionName");
            return (String) method.invoke(null);
        } catch (Exception e) {
            return "Unknown";
        }
    }

    private boolean isIntentAvailable(Intent intent) {
        PackageManager packageManager = getReactApplicationContext().getPackageManager();
        return intent.resolveActivity(packageManager) != null;
    }

    private void optimizeForMIUI() {
        // Implement MIUI-specific optimizations
        Log.d(TAG, "Applying MIUI-specific optimizations");
        
        // You can add specific MIUI performance tweaks here
        // For example, adjusting thread priorities, memory management, etc.
    }
}
