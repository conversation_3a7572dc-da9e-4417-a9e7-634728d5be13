<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- MIUI Performance Optimizations -->
    <performance-config>
        <!-- Disable MIUI performance monitoring for this app -->
        <disable-miui-performance-monitoring>true</disable-miui-performance-monitoring>
        
        <!-- Request high performance mode -->
        <request-high-performance>true</request-high-performance>
        
        <!-- Optimize for React Native -->
        <react-native-optimizations>
            <js-thread-priority>high</js-thread-priority>
            <ui-thread-priority>high</ui-thread-priority>
            <background-processing>enabled</background-processing>
        </react-native-optimizations>
        
        <!-- Memory management -->
        <memory-management>
            <large-heap>true</large-heap>
            <heap-growth-limit>512m</heap-growth-limit>
            <max-heap-size>1024m</max-heap-size>
        </memory-management>
        
        <!-- Battery optimization exclusion -->
        <battery-optimization>
            <exclude-from-doze>true</exclude-from-doze>
            <exclude-from-standby>true</exclude-from-standby>
        </battery-optimization>
    </performance-config>
</resources>
