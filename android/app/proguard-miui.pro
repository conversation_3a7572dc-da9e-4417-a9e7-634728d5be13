# MIUI-specific ProGuard rules for better performance

# Keep MIUI-specific classes
-keep class miui.** { *; }
-keep class com.miui.** { *; }
-keep class com.xiaomi.** { *; }

# React Native optimizations for MIUI
-keep class com.facebook.react.** { *; }
-keep class com.facebook.hermes.** { *; }
-keep class com.facebook.jni.** { *; }

# Performance optimizations
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# MIUI performance monitoring exclusions
-dontwarn miui.performance.**
-dontwarn com.miui.performance.**

# Keep React Native bridge methods
-keepclassmembers class * {
    @com.facebook.react.uimanager.annotations.ReactProp <methods>;
    @com.facebook.react.uimanager.annotations.ReactPropGroup <methods>;
}

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# MIUI-specific optimizations
-keep class * extends com.facebook.react.ReactPackage { *; }
-keep class * extends com.facebook.react.bridge.ReactContextBaseJavaModule { *; }
-keep class * extends com.facebook.react.bridge.BaseJavaModule { *; }

# Memory optimization
-dontshrink
-dontoptimize

# Keep Mattermost-specific classes
-keep class com.mattermost.** { *; }

# MIUI system integration
-keep class android.miui.** { *; }
-dontwarn android.miui.**

# Performance monitoring
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}
